const {
    ValidationError,
    DatabaseError,
    ConnectionError,
    ConnectionAcquireTimeoutError,
    ConnectionRefusedError,
    HostNotFoundError,
    HostNotReachableError,
    InvalidConnectionError,
    ConnectionTimedOutError
} = require("sequelize");
const { ApiError } = require("../helpers/api.helper");
const logger = require("../config/logger");

// Error Response Function
const sendError = (res, message, code = 400, data = []) => {
    res.status(code).send({
        status: false,
        message,
        data,
    });
};

const errorConverter = (err, req, res, next) => {
    let error = err;
    if (!(error instanceof ApiError)) {
        let statusCode = 500;
        let message = "Internal Server Error";

        if (error instanceof ValidationError) {
            statusCode = 400;
            message = error.message;
        } else if (error instanceof ConnectionAcquireTimeoutError) {
            statusCode = 503;
            message = "Database is temporarily unavailable. Please try again later.";
            logger.error('Database connection pool timeout:', {
                error: error.message,
                stack: error.stack,
                url: req?.url,
                method: req?.method
            });
        } else if (error instanceof ConnectionRefusedError ||
                   error instanceof HostNotFoundError ||
                   error instanceof HostNotReachableError ||
                   error instanceof InvalidConnectionError ||
                   error instanceof ConnectionTimedOutError) {
            statusCode = 503;
            message = "Database connection failed. Please try again later.";
            logger.error('Database connection error:', {
                error: error.message,
                type: error.constructor.name,
                stack: error.stack,
                url: req?.url,
                method: req?.method
            });
        } else if (error instanceof DatabaseError) {
            statusCode = 500;
            message = "Database operation failed";
            logger.error('Database operation error:', {
                error: error.message,
                stack: error.stack,
                url: req?.url,
                method: req?.method
            });
        } else if (error instanceof ConnectionError) {
            statusCode = 503;
            message = "Database connection error. Please try again later.";
            logger.error('General database connection error:', {
                error: error.message,
                stack: error.stack,
                url: req?.url,
                method: req?.method
            });
        } else if (error.statusCode) {
            statusCode = error.statusCode;
            message = error.message || "Internal Server Error";
        }

        error = new ApiError(statusCode, message, false, err.stack);
    }
    next(error);
};

// eslint-disable-next-line no-unused-vars
const errorHandler = (err, req, res, next) => {
    let { statusCode, message } = err;
    const isProduction = process.env.NODE_ENV === "production";

    if (isProduction && !err.isOperational) {
        statusCode = 500;
        message = "Internal Server Error";
    }

    res.locals.errorMessage = err.message;

    // Log the error
    logger.error(`${req.method} ${req.url} - ${statusCode} - ${message}`, {
        error: err.message,
        stack: err.stack,
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
    });

    let data = [];
    if (!isProduction) {
        data = err.stack;
    }

    return sendError(res, message, statusCode, data);
};

module.exports = {
    errorConverter,
    errorHandler,
};

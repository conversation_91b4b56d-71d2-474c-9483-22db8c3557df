require('dotenv').config(); // 👈 Load environment variables from .env

const baseConfig = {
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || null,
  database: process.env.DB_NAME || 'gold',
  host: process.env.DB_HOST || '127.0.0.1',
  dialect: process.env.DB_DIALECT || 'mysql',
  pool: {
    max: parseInt(process.env.DB_POOL_MAX) || 10,
    min: parseInt(process.env.DB_POOL_MIN) || 0,
    acquire: parseInt(process.env.DB_POOL_ACQUIRE) || 60000, // 60 seconds
    idle: parseInt(process.env.DB_POOL_IDLE) || 10000, // 10 seconds
    evict: parseInt(process.env.DB_POOL_EVICT) || 1000, // 1 second
    handleDisconnects: true
  },
  retry: {
    match: [
      /ConnectionError/,
      /SequelizeConnectionError/,
      /SequelizeConnectionRefusedError/,
      /SequelizeHostNotFoundError/,
      /SequelizeHostNotReachableError/,
      /SequelizeInvalidConnectionError/,
      /SequelizeConnectionTimedOutError/,
      /ConnectionAcquireTimeoutError/
    ],
    max: 3
  },
  dialectOptions: {
    connectTimeout: parseInt(process.env.DB_CONNECT_TIMEOUT) || 60000,
    acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT) || 60000,
    timeout: parseInt(process.env.DB_TIMEOUT) || 60000,
  }
};

module.exports = {
  development: baseConfig,
  test: baseConfig,
  production: baseConfig
};

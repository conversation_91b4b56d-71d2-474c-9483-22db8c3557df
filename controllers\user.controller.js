const { User, Referral } = require('../models');
const BaseController = require('../helpers/BaseController');
const { handleDatabaseOperation } = require('../helpers/api.helper');
const { Op } = require('sequelize');
const logger = require('../config/logger');

exports.getProfile = async (req, res, next) => {
  try {
    const userId = req.user.id;

    const user = await handleDatabaseOperation(async () => {
      return await User.findByPk(userId, {
        attributes: ['user_id', 'user_name', 'name', 'email', 'mobile', 'referral_code', 'createdAt']
      });
    });

    if (!user) {
      return BaseController.sendError(res, 'User not found');
    }

    return BaseController.sendResponse(res, user, 'User profile fetched successfully');
  } catch (err) {
    logger.error('Get profile error:', err);
    next(err); // Pass to error middleware
  }
};

exports.getReferralCount = async (req, res, next) => {
  try {
    const userId = req.user.id;

    // Check if user exists with retry logic
    const user = await handleDatabaseOperation(async () => {
      return await User.findByPk(userId, {
        attributes: ['user_id', 'user_name']
      });
    });

    if (!user) {
      return BaseController.sendError(res, 'User not found');
    }

    // Get level 1 count with retry logic
    const level1Count = await handleDatabaseOperation(async () => {
      return await Referral.count({
        where: {
          referrer_id: userId,
          level: 1,
          status: 'active'
        }
      });
    });

    // Get level 2 & 3 combined count with retry logic
    const level2And3Count = await handleDatabaseOperation(async () => {
      return await Referral.count({
        where: {
          referrer_id: userId,
          level: {
            [Op.in]: [2, 3]
          },
          status: 'active'
        }
      });
    });

    const referralData = {
      level_1: level1Count,
      level_2_and_3: level2And3Count,
      total: level1Count + level2And3Count
    };

    return BaseController.sendResponse(res, referralData, 'Referral count fetched successfully');
  } catch (err) {
    logger.error('Get referral count error:', err);
    next(err); // Pass to error middleware
  }
};

class ApiError extends Error {
    constructor(statusCode, message, isOperational = true, stack = '') {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        if (stack) {
            this.stack = stack;
        } else {
            Error.captureStackTrace(this, this.constructor);
        }
    }
}

// Helper function to handle database operations with retry logic
const handleDatabaseOperation = async (operation, retries = 3, delay = 1000) => {
    for (let attempt = 1; attempt <= retries; attempt++) {
        try {
            return await operation();
        } catch (error) {
            // Check if it's a connection timeout or connection error
            if (error.name === 'SequelizeConnectionAcquireTimeoutError' ||
                error.name === 'ConnectionAcquireTimeoutError' ||
                error.name === 'SequelizeConnectionError') {

                if (attempt === retries) {
                    throw new ApiError(503, 'Database is temporarily unavailable. Please try again later.');
                }

                // Wait before retrying
                await new Promise(resolve => setTimeout(resolve, delay * attempt));
                continue;
            }

            // For other errors, throw immediately
            throw error;
        }
    }
};

module.exports = { ApiError, handleDatabaseOperation };

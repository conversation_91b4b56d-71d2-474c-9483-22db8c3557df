const jwt = require('jsonwebtoken');
const { ApiError } = require('../helpers/api.helper');
const logger = require('../config/logger');

exports.verifyToken = (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];

        // Check for Bearer token
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new ApiError(403, 'Authorization token is missing or malformed');
        }

        const token = authHeader.split(' ')[1];

        try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET || 'mysecretkey');
            req.user = decoded; // { id: ..., user_id: ..., user_name: ..., email: ..., role: ... }

            // Check if user has user role
            if (decoded.role !== 'user') {
                throw new ApiError(403, 'Access denied. User role required.');
            }

            next();
        } catch (jwtErr) {
            if (jwtErr.name === 'TokenExpiredError') {
                logger.warn(`JWT token expired for user: ${jwtErr.expiredAt}`);
                throw new ApiError(401, 'Token has expired. Please login again.');
            } else if (jwtErr.name === 'JsonWebTokenError') {
                logger.warn(`Invalid JWT token: ${jwtErr.message}`);
                throw new ApiError(401, 'Invalid token. Please login again.');
            } else {
                throw jwtErr; // Re-throw if it's already an ApiError
            }
        }
    } catch (err) {
        next(err); // Pass to error middleware
    }
};

exports.verifyAdmin = (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];

        // Check for Bearer token
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new ApiError(403, 'Authorization token is missing or malformed');
        }

        const token = authHeader.split(' ')[1];

        try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET || 'mysecretkey');
            req.user = decoded; // { id: ..., user_id: ..., user_name: ..., email: ..., role: ... }

            // Check if user has admin role
            if (decoded.role !== 'admin') {
                throw new ApiError(403, 'Access denied. Admin role required.');
            }

            next();
        } catch (jwtErr) {
            if (jwtErr.name === 'TokenExpiredError') {
                logger.warn(`JWT token expired for admin user: ${jwtErr.expiredAt}`);
                throw new ApiError(401, 'Token has expired. Please login again.');
            } else if (jwtErr.name === 'JsonWebTokenError') {
                logger.warn(`Invalid JWT token for admin: ${jwtErr.message}`);
                throw new ApiError(401, 'Invalid token. Please login again.');
            } else {
                throw jwtErr; // Re-throw if it's already an ApiError
            }
        }
    } catch (err) {
        next(err); // Pass to error middleware
    }
};
